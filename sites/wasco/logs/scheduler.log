2025-08-04 10:05:37,146 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-08-04 10:05:37,154 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for wasco
2025-08-04 10:05:37,155 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 10:05:37,158 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-08-04 10:05:37,168 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 10:05:37,173 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 10:05:37,178 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:05:37,180 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-08-04 10:05:37,190 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for wasco
2025-08-04 10:05:37,203 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-08-04 10:05:37,210 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-08-04 10:05:37,212 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:05:37,215 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-08-04 10:05:37,222 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-08-04 10:05:37,224 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 10:05:37,225 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-08-04 10:05:37,240 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-08-04 10:05:37,243 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-08-04 10:06:38,065 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-08-04 10:06:38,069 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-08-04 10:06:38,070 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for wasco
2025-08-04 10:06:38,076 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for wasco
2025-08-04 10:06:38,077 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-08-04 10:06:38,079 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-08-04 10:06:38,084 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 10:06:38,088 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 10:06:38,093 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for wasco
2025-08-04 10:06:38,097 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_tool.bom_update_tool.auto_update_latest_price_in_all_boms because it was found in queue for wasco
2025-08-04 10:06:38,102 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-08-04 10:06:38,109 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:06:38,112 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-08-04 10:06:38,115 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for wasco
2025-08-04 10:06:38,122 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-08-04 10:06:38,123 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-08-04 10:06:38,124 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-08-04 10:06:38,126 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 10:06:38,127 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-08-04 10:06:38,129 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:06:38,143 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-08-04 10:06:38,146 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 10:06:38,151 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 10:06:38,156 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-08-04 10:06:38,159 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-08-04 10:06:38,161 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 10:06:38,162 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-08-04 10:07:38,879 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-08-04 10:07:38,883 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-08-04 10:07:38,885 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 10:07:38,886 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:07:38,888 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 10:07:38,890 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-08-04 10:07:38,902 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-08-04 10:07:38,911 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 10:07:38,914 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-08-04 10:07:38,924 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 10:07:38,929 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-08-04 10:07:38,931 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:07:38,934 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-08-04 10:07:38,939 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-08-04 10:07:38,946 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-08-04 10:07:38,951 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-08-04 10:07:38,959 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-08-04 10:07:38,969 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-08-04 10:08:39,182 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-08-04 10:08:39,184 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-08-04 10:08:39,187 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-08-04 10:08:39,204 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:08:39,243 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 10:08:39,247 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:08:39,281 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-08-04 10:08:39,287 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-08-04 10:08:39,299 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-08-04 10:08:39,302 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-08-04 10:08:39,306 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-08-04 10:08:39,324 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-08-04 10:08:39,341 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-08-04 10:08:39,350 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 10:08:39,356 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-08-04 10:08:39,362 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-08-04 10:08:39,369 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 10:08:39,374 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 10:09:40,644 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for wasco
2025-08-04 10:09:40,648 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-08-04 10:09:40,651 ERROR scheduler Skipped queueing erpnext.setup.doctype.email_digest.email_digest.send because it was found in queue for wasco
2025-08-04 10:09:40,652 ERROR scheduler Skipped queueing hrms.hr.utils.allocate_earned_leaves because it was found in queue for wasco
2025-08-04 10:09:40,654 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 10:09:40,657 ERROR scheduler Skipped queueing erpnext.assets.doctype.asset.depreciation.post_depreciation_entries because it was found in queue for wasco
2025-08-04 10:09:40,662 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for wasco
2025-08-04 10:09:40,668 ERROR scheduler Skipped queueing hrms.hr.utils.generate_leave_encashment because it was found in queue for wasco
2025-08-04 10:09:40,674 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 10:09:40,682 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_subscription.process_subscription.create_subscription_process because it was found in queue for wasco
2025-08-04 10:09:40,685 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:09:40,691 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-08-04 10:09:40,694 ERROR scheduler Skipped queueing hrms.hr.doctype.leave_ledger_entry.leave_ledger_entry.process_expired_allocation because it was found in queue for wasco
2025-08-04 10:09:40,695 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for wasco
2025-08-04 10:09:40,703 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 10:09:40,706 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for wasco
2025-08-04 10:09:40,707 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 10:09:40,709 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for wasco
2025-08-04 10:09:40,714 ERROR scheduler Skipped queueing erpnext.crm.utils.open_leads_opportunities_based_on_todays_event because it was found in queue for wasco
2025-08-04 10:09:40,719 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for wasco
2025-08-04 10:09:40,720 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 10:09:40,723 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 11:01:20,812 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 11:01:20,832 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 11:01:20,872 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 11:01:20,882 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 11:25:10,743 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-08-04 11:25:10,773 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 11:25:10,831 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-08-04 11:25:10,866 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 12:01:03,082 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for wasco
2025-08-04 12:01:03,108 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-08-04 12:01:03,112 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for wasco
2025-08-04 12:01:03,124 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for wasco
2025-08-04 12:01:03,126 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-08-04 12:01:03,129 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-08-04 12:01:03,132 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-08-04 12:01:03,136 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for wasco
2025-08-04 12:01:03,138 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for wasco
2025-08-04 12:01:03,141 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.parking_bill.parking_bill.check_bills_all_vehicles because it was found in queue for wasco
2025-08-04 12:01:03,143 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 12:01:03,152 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 12:01:03,160 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-08-04 12:01:03,162 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for wasco
2025-08-04 12:01:03,176 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for wasco
2025-08-04 12:01:03,186 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for wasco
2025-08-04 12:01:03,197 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for wasco
2025-08-04 12:01:03,210 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for wasco
2025-08-04 12:01:03,217 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for wasco
2025-08-04 12:01:03,233 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-08-04 12:01:03,243 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for wasco
2025-08-04 12:01:03,248 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-08-04 12:01:03,251 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for wasco
2025-08-04 12:01:03,261 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 12:01:03,272 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for wasco
2025-08-04 12:01:03,275 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for wasco
2025-08-04 12:01:03,278 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for wasco
2025-08-04 12:01:03,283 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-08-04 12:01:03,288 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for wasco
2025-08-04 12:01:03,293 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-08-04 12:01:03,299 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for wasco
2025-08-04 12:01:03,303 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-08-04 12:01:03,306 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-08-04 12:01:03,312 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 12:01:03,320 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for wasco
2025-08-04 12:01:03,327 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for wasco
2025-08-04 12:01:03,335 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-08-04 12:01:03,339 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for wasco
2025-08-04 12:02:05,354 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 12:02:05,366 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 12:02:05,404 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 12:02:05,406 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 12:03:05,748 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 12:03:05,805 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 12:03:05,811 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 12:03:05,817 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 15:15:56,577 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 15:15:56,598 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 15:15:56,620 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 15:15:56,660 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 15:16:57,368 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 15:16:57,378 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 15:16:57,387 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-08-04 15:16:57,390 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-08-04 15:16:57,397 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-08-04 15:16:57,400 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-08-04 15:16:57,402 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-08-04 15:16:57,415 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-08-04 15:16:57,421 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 15:16:57,422 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-08-04 15:16:57,435 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-08-04 15:16:57,445 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-08-04 15:16:57,450 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 15:16:57,451 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-08-04 15:16:57,457 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-08-04 15:16:57,458 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco

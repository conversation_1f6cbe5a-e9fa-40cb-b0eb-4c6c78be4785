2025-08-04 12:02:05,595 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 12:02:05,599 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 12:02:05,616 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 12:02:05,621 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 12:02:05,631 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 12:02:05,652 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 12:02:05,667 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 12:02:05,672 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 12:02:05,677 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 12:02:05,685 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 12:02:05,693 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 12:02:05,699 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 12:03:05,858 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 12:03:05,860 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 12:03:05,866 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 12:03:05,875 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 12:03:05,899 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 12:03:05,911 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 12:03:05,915 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 12:03:05,919 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 12:03:05,952 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 12:03:05,958 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 12:03:05,961 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 12:03:05,969 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for beetle
2025-08-04 12:03:05,970 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 12:57:44,977 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for beetle
2025-08-04 12:57:45,014 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for beetle
2025-08-04 12:57:45,056 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for beetle
2025-08-04 12:57:45,061 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for beetle
2025-08-04 12:57:45,066 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for beetle
2025-08-04 15:15:56,117 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for thps.or.tz
2025-08-04 15:15:56,125 ERROR scheduler Skipped queueing helpdesk.search.build_index_if_not_exists because it was found in queue for thps.or.tz
2025-08-04 15:15:56,140 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for thps.or.tz
2025-08-04 15:15:56,142 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for thps.or.tz
2025-08-04 15:15:56,144 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for thps.or.tz
2025-08-04 15:15:56,147 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for thps.or.tz
2025-08-04 15:15:56,151 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for thps.or.tz
2025-08-04 15:15:56,154 ERROR scheduler Skipped queueing helpdesk.search.download_corpus because it was found in queue for thps.or.tz
2025-08-04 15:15:56,166 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for thps.or.tz
2025-08-04 15:15:56,168 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for thps.or.tz
2025-08-04 15:15:56,170 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for thps.or.tz
2025-08-04 15:15:56,174 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for thps.or.tz
2025-08-04 15:15:56,176 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for thps.or.tz
2025-08-04 15:15:56,179 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for thps.or.tz
2025-08-04 15:15:56,181 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for thps.or.tz
2025-08-04 15:15:56,190 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for thps.or.tz
2025-08-04 15:15:56,197 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for thps.or.tz
2025-08-04 15:15:56,198 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for thps.or.tz
2025-08-04 15:15:56,202 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for thps.or.tz
2025-08-04 15:15:56,203 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for thps.or.tz
2025-08-04 15:15:56,205 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for thps.or.tz
2025-08-04 15:15:56,207 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for thps.or.tz
2025-08-04 15:15:56,214 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for thps.or.tz
2025-08-04 15:15:56,217 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for thps.or.tz
2025-08-04 15:15:56,219 ERROR scheduler Skipped queueing birthday_wishes_hourly because it was found in queue for thps.or.tz
2025-08-04 15:15:56,225 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for thps.or.tz
2025-08-04 15:15:56,226 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for thps.or.tz
2025-08-04 15:15:56,228 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for thps.or.tz
2025-08-04 15:15:56,232 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for thps.or.tz
2025-08-04 15:15:56,233 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for thps.or.tz
2025-08-04 15:15:56,239 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for thps.or.tz
2025-08-04 15:15:56,243 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for thps.or.tz
2025-08-04 15:15:56,245 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for thps.or.tz
2025-08-04 15:15:56,250 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for thps.or.tz
2025-08-04 15:15:56,252 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for thps.or.tz
2025-08-04 15:15:56,254 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for thps.or.tz
2025-08-04 15:15:56,261 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for thps.or.tz
2025-08-04 15:15:56,283 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for beetle
2025-08-04 15:15:56,285 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for beetle
2025-08-04 15:15:56,286 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for beetle
2025-08-04 15:15:56,292 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 15:15:56,302 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for beetle
2025-08-04 15:15:56,309 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for beetle
2025-08-04 15:15:56,314 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 15:15:56,316 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for beetle
2025-08-04 15:15:56,320 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for beetle
2025-08-04 15:15:56,327 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for beetle
2025-08-04 15:15:56,334 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 15:15:56,342 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for beetle
2025-08-04 15:15:56,346 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for beetle
2025-08-04 15:15:56,347 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 15:15:56,350 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for beetle
2025-08-04 15:15:56,354 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for beetle
2025-08-04 15:15:56,358 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for beetle
2025-08-04 15:15:56,380 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 15:15:56,384 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 15:15:56,396 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for beetle
2025-08-04 15:15:56,401 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for beetle
2025-08-04 15:15:56,413 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 15:15:56,417 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for beetle
2025-08-04 15:15:56,429 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 15:15:56,431 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for beetle
2025-08-04 15:15:56,435 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for beetle
2025-08-04 15:15:56,441 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for beetle
2025-08-04 15:15:56,444 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 15:15:56,450 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 15:15:56,465 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 15:15:56,467 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for beetle
2025-08-04 15:15:56,475 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 15:15:56,478 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for beetle
2025-08-04 15:15:56,480 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for beetle
2025-08-04 15:15:56,500 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for beetle
2025-08-04 15:15:56,504 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for beetle
2025-08-04 15:15:56,509 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for beetle
2025-08-04 15:15:56,577 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 15:15:56,598 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 15:15:56,620 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 15:15:56,660 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 15:15:56,690 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for attendance
2025-08-04 15:15:56,694 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for attendance
2025-08-04 15:15:56,697 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 15:15:56,706 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for attendance
2025-08-04 15:15:56,709 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for attendance
2025-08-04 15:15:56,713 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for attendance
2025-08-04 15:15:56,716 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 15:15:56,719 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for attendance
2025-08-04 15:15:56,726 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for attendance
2025-08-04 15:15:56,733 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for attendance
2025-08-04 15:15:56,742 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 15:15:56,745 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 15:15:56,755 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for attendance
2025-08-04 15:15:56,760 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for attendance
2025-08-04 15:15:56,770 ERROR scheduler Skipped queueing csf_tz.csf_tz.doctype.vehicle_fine_record.vehicle_fine_record.check_fine_all_vehicles because it was found in queue for attendance
2025-08-04 15:15:56,780 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 15:15:56,782 ERROR scheduler Skipped queueing biometric_client.biometric_client.doctype.biometric_data_staging.biometric_data_staging.process_biometric_logs because it was found in queue for attendance
2025-08-04 15:15:56,791 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 15:15:56,796 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for attendance
2025-08-04 15:15:56,803 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 15:15:56,806 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for attendance
2025-08-04 15:15:56,807 ERROR scheduler Skipped queueing erpnext.accounts.doctype.gl_entry.gl_entry.rename_gle_sle_docs because it was found in queue for attendance
2025-08-04 15:15:56,809 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for attendance
2025-08-04 15:15:56,817 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for attendance
2025-08-04 15:15:56,822 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 15:15:56,825 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 15:15:56,831 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for attendance
2025-08-04 15:15:56,834 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for attendance
2025-08-04 15:15:56,835 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 15:15:56,836 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 15:15:56,840 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for attendance
2025-08-04 15:15:56,841 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for attendance
2025-08-04 15:15:56,846 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for attendance
2025-08-04 15:15:56,850 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 15:15:56,855 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for attendance
2025-08-04 15:15:56,858 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for attendance
2025-08-04 15:15:56,860 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 15:15:56,880 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for certifications
2025-08-04 15:15:56,889 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for certifications
2025-08-04 15:15:56,893 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for certifications
2025-08-04 15:15:56,897 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for certifications
2025-08-04 15:15:56,902 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for certifications
2025-08-04 15:15:56,904 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for certifications
2025-08-04 15:15:56,907 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for certifications
2025-08-04 15:15:56,912 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for certifications
2025-08-04 15:15:56,915 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for certifications
2025-08-04 15:15:56,918 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for certifications
2025-08-04 15:15:56,924 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for certifications
2025-08-04 15:15:56,926 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for certifications
2025-08-04 15:15:56,931 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for certifications
2025-08-04 15:15:56,933 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for certifications
2025-08-04 15:15:56,935 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for certifications
2025-08-04 15:15:56,937 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for certifications
2025-08-04 15:15:56,939 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for certifications
2025-08-04 15:15:56,941 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for certifications
2025-08-04 15:15:56,961 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 15:15:56,964 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 15:15:56,968 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.hourly_reminder because it was found in queue for masumin
2025-08-04 15:15:56,970 ERROR scheduler Skipped queueing hrms.hr.doctype.daily_work_summary_group.daily_work_summary_group.trigger_emails because it was found in queue for masumin
2025-08-04 15:15:56,975 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for masumin
2025-08-04 15:15:56,977 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_get_transactions because it was found in queue for masumin
2025-08-04 15:15:56,978 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for masumin
2025-08-04 15:15:56,981 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 15:15:56,983 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 15:15:56,986 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for masumin
2025-08-04 15:15:56,988 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for masumin
2025-08-04 15:15:56,992 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.collect_project_status because it was found in queue for masumin
2025-08-04 15:15:56,994 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 15:15:57,002 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for masumin
2025-08-04 15:15:57,007 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for masumin
2025-08-04 15:15:57,010 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for masumin
2025-08-04 15:15:57,012 ERROR scheduler Skipped queueing vfd_providers.utils.utils.posting_all_vfd_invoices because it was found in queue for masumin
2025-08-04 15:15:57,013 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 15:15:57,015 ERROR scheduler Skipped queueing payware.payware.doctype.biometric_settings.biometric_settings.auto_make_employee_checkin because it was found in queue for masumin
2025-08-04 15:15:57,020 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for masumin
2025-08-04 15:15:57,025 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for masumin
2025-08-04 15:15:57,030 ERROR scheduler Skipped queueing vfd_providers.vfd_providers.doctype.simplify_vfd_settings.simplify_vfd_settings.get_token because it was found in queue for masumin
2025-08-04 15:15:57,032 ERROR scheduler Skipped queueing erpnext.erpnext_integrations.doctype.plaid_settings.plaid_settings.automatic_synchronization because it was found in queue for masumin
2025-08-04 15:15:57,039 ERROR scheduler Skipped queueing erpnext.projects.doctype.project.project.project_status_update_reminder because it was found in queue for masumin
2025-08-04 15:15:57,040 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 15:15:57,045 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 15:15:57,050 ERROR scheduler Skipped queueing erpnext.utilities.doctype.video.video.update_youtube_data because it was found in queue for masumin
2025-08-04 15:15:57,054 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for masumin
2025-08-04 15:15:57,055 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 15:15:57,061 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 15:15:57,063 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for masumin
2025-08-04 15:15:57,065 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 15:15:57,066 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for masumin
2025-08-04 15:15:57,069 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 15:15:57,073 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 15:15:57,076 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for masumin
2025-08-04 15:15:57,081 ERROR scheduler Skipped queueing payments.payment_gateways.doctype.razorpay_settings.razorpay_settings.capture_payment because it was found in queue for masumin
2025-08-04 15:15:57,101 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for integration
2025-08-04 15:15:57,104 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for integration
2025-08-04 15:15:57,107 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 15:15:57,110 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_daily_updates because it was found in queue for integration
2025-08-04 15:15:57,112 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for integration
2025-08-04 15:15:57,115 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-08-04 15:15:57,116 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_monthly_summary because it was found in queue for integration
2025-08-04 15:15:57,118 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_contacts.google_contacts.sync because it was found in queue for integration
2025-08-04 15:15:57,119 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 15:15:57,121 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for integration
2025-08-04 15:15:57,123 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.remove_unverified_record because it was found in queue for integration
2025-08-04 15:15:57,124 ERROR scheduler Skipped queueing frappe.email.doctype.newsletter.newsletter.send_scheduled_email because it was found in queue for integration
2025-08-04 15:15:57,126 ERROR scheduler Skipped queueing frappe.email.doctype.notification.notification.trigger_daily_alerts because it was found in queue for integration
2025-08-04 15:15:57,128 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 15:15:57,129 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for integration
2025-08-04 15:15:57,131 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 15:15:57,133 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for integration
2025-08-04 15:15:57,134 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_settings.energy_point_settings.allocate_review_points because it was found in queue for integration
2025-08-04 15:15:57,136 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for integration
2025-08-04 15:15:57,137 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-08-04 15:15:57,139 ERROR scheduler Skipped queueing frappe.automation.doctype.auto_repeat.auto_repeat.make_auto_repeat_entry because it was found in queue for integration
2025-08-04 15:15:57,141 ERROR scheduler Skipped queueing frappe.monitor.flush because it was found in queue for integration
2025-08-04 15:15:57,143 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for integration
2025-08-04 15:15:57,145 ERROR scheduler Skipped queueing frappe.desk.doctype.event.event.send_event_digest because it was found in queue for integration
2025-08-04 15:15:57,146 ERROR scheduler Skipped queueing frappe.sessions.clear_expired_sessions because it was found in queue for integration
2025-08-04 15:15:57,148 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for integration
2025-08-04 15:15:57,150 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-08-04 15:15:57,152 ERROR scheduler Skipped queueing frappe.core.doctype.log_settings.log_settings.run_log_clean_up because it was found in queue for integration
2025-08-04 15:15:57,153 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 15:15:57,155 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-08-04 15:15:57,156 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 15:15:57,158 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for integration
2025-08-04 15:15:57,159 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for integration
2025-08-04 15:15:57,161 ERROR scheduler Skipped queueing frappe.desk.notifications.clear_notifications because it was found in queue for integration
2025-08-04 15:15:57,162 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for integration
2025-08-04 15:15:57,164 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_monthly because it was found in queue for integration
2025-08-04 15:15:57,165 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for integration
2025-08-04 15:15:57,167 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for integration
2025-08-04 15:15:57,169 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for integration
2025-08-04 15:15:57,171 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 15:15:57,172 ERROR scheduler Skipped queueing frappe.model.utils.link_count.update_link_count because it was found in queue for integration
2025-08-04 15:15:57,174 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 15:16:57,216 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for certifications
2025-08-04 15:16:57,240 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for certifications
2025-08-04 15:16:57,242 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for certifications
2025-08-04 15:16:57,246 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for certifications
2025-08-04 15:16:57,247 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for certifications
2025-08-04 15:16:57,251 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for certifications
2025-08-04 15:16:57,254 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for certifications
2025-08-04 15:16:57,255 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for certifications
2025-08-04 15:16:57,269 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for thps.or.tz
2025-08-04 15:16:57,280 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for thps.or.tz
2025-08-04 15:16:57,285 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for thps.or.tz
2025-08-04 15:16:57,288 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for thps.or.tz
2025-08-04 15:16:57,293 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for thps.or.tz
2025-08-04 15:16:57,302 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for thps.or.tz
2025-08-04 15:16:57,306 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for thps.or.tz
2025-08-04 15:16:57,321 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for thps.or.tz
2025-08-04 15:16:57,322 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for thps.or.tz
2025-08-04 15:16:57,337 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for thps.or.tz
2025-08-04 15:16:57,346 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for thps.or.tz
2025-08-04 15:16:57,348 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for thps.or.tz
2025-08-04 15:16:57,353 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for thps.or.tz
2025-08-04 15:16:57,368 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for wasco
2025-08-04 15:16:57,378 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for wasco
2025-08-04 15:16:57,387 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for wasco
2025-08-04 15:16:57,390 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.notify_unreplied because it was found in queue for wasco
2025-08-04 15:16:57,397 ERROR scheduler Skipped queueing erpnext.accounts.doctype.process_payment_reconciliation.process_payment_reconciliation.trigger_reconciliation_for_queued_docs because it was found in queue for wasco
2025-08-04 15:16:57,400 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.process_download_files because it was found in queue for wasco
2025-08-04 15:16:57,402 ERROR scheduler Skipped queueing frappe.automation.doctype.reminder.reminder.send_reminders because it was found in queue for wasco
2025-08-04 15:16:57,415 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for wasco
2025-08-04 15:16:57,421 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for wasco
2025-08-04 15:16:57,422 ERROR scheduler Skipped queueing frappe.deferred_insert.save_to_db because it was found in queue for wasco
2025-08-04 15:16:57,435 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for wasco
2025-08-04 15:16:57,445 ERROR scheduler Skipped queueing frappe.utils.global_search.sync_global_search because it was found in queue for wasco
2025-08-04 15:16:57,450 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_assignment_schedule.shift_assignment_schedule.process_auto_shift_creation because it was found in queue for wasco
2025-08-04 15:16:57,451 ERROR scheduler Skipped queueing csf_tz.csftz_hooks.items_revaluation.process_incorrect_balance_qty because it was found in queue for wasco
2025-08-04 15:16:57,457 ERROR scheduler Skipped queueing erpnext.manufacturing.doctype.bom_update_log.bom_update_log.resume_bom_cost_update_jobs because it was found in queue for wasco
2025-08-04 15:16:57,458 ERROR scheduler Skipped queueing csf_tz.stanbic.sftp.sync_all_stanbank_files because it was found in queue for wasco
2025-08-04 15:16:57,475 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for beetle
2025-08-04 15:16:57,477 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for beetle
2025-08-04 15:16:57,481 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for beetle
2025-08-04 15:16:57,484 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for beetle
2025-08-04 15:16:57,486 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for beetle
2025-08-04 15:16:57,502 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for beetle
2025-08-04 15:16:57,514 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for beetle
2025-08-04 15:16:57,517 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for beetle
2025-08-04 15:16:57,522 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for beetle
2025-08-04 15:16:57,526 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for beetle
2025-08-04 15:16:57,542 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for beetle
2025-08-04 15:16:57,560 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for beetle
2025-08-04 15:16:57,573 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for attendance
2025-08-04 15:16:57,574 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for attendance
2025-08-04 15:16:57,578 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for attendance
2025-08-04 15:16:57,591 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for attendance
2025-08-04 15:16:57,603 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for attendance
2025-08-04 15:16:57,608 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for attendance
2025-08-04 15:16:57,626 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for attendance
2025-08-04 15:16:57,633 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for attendance
2025-08-04 15:16:57,638 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for attendance
2025-08-04 15:16:57,640 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for attendance
2025-08-04 15:16:57,648 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for attendance
2025-08-04 15:16:57,649 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for attendance
2025-08-04 15:16:57,651 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for attendance
2025-08-04 15:16:57,670 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 15:16:57,673 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 15:16:57,677 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 15:16:57,700 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 15:16:57,704 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 15:16:57,712 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 15:16:57,717 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 15:16:57,720 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 15:16:57,725 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 15:16:57,734 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 15:16:57,738 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 15:16:57,751 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 15:16:57,752 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 15:16:57,764 ERROR scheduler Skipped queueing frappe.social.doctype.energy_point_log.energy_point_log.send_weekly_summary because it was found in queue for integration
2025-08-04 15:16:57,766 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_weekly_updates because it was found in queue for integration
2025-08-04 15:16:57,769 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.daily_backup because it was found in queue for integration
2025-08-04 15:16:57,773 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_drive.google_drive.weekly_backup because it was found in queue for integration
2025-08-04 15:16:57,777 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_weekly because it was found in queue for integration
2025-08-04 15:16:57,779 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_monthly because it was found in queue for integration
2025-08-04 15:16:57,780 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_daily because it was found in queue for integration
2025-08-04 15:16:57,791 ERROR scheduler Skipped queueing frappe.utils.change_log.check_for_update because it was found in queue for integration
2025-08-04 15:16:57,795 ERROR scheduler Skipped queueing frappe.email.doctype.auto_email_report.auto_email_report.send_daily because it was found in queue for integration
2025-08-04 15:16:57,805 ERROR scheduler Skipped queueing frappe.integrations.doctype.s3_backup_settings.s3_backup_settings.take_backups_daily because it was found in queue for integration
2025-08-04 15:16:57,808 ERROR scheduler Skipped queueing frappe.desk.doctype.changelog_feed.changelog_feed.fetch_changelog_feed because it was found in queue for integration
2025-08-04 15:16:57,811 ERROR scheduler Skipped queueing frappe.integrations.doctype.dropbox_settings.dropbox_settings.take_backups_weekly because it was found in queue for integration

2025-08-04 12:02:05,476 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 12:02:05,480 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 12:02:05,486 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 12:02:05,491 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 12:02:05,498 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 12:02:05,512 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 12:02:05,523 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 12:02:05,524 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 12:02:05,529 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 12:02:05,542 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 12:02:05,552 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 12:02:05,558 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for masumin
2025-08-04 12:02:05,567 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 12:02:05,571 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 12:03:06,075 ERROR scheduler Skipped queueing erpnext.stock.doctype.repost_item_valuation.repost_item_valuation.repost_entries because it was found in queue for masumin
2025-08-04 12:03:06,081 ERROR scheduler Skipped queueing frappe.email.doctype.email_account.email_account.pull because it was found in queue for masumin
2025-08-04 12:03:06,098 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.process_auto_attendance_for_all_shifts because it was found in queue for masumin
2025-08-04 12:03:06,101 ERROR scheduler Skipped queueing erpnext.utilities.bulk_transaction.retry because it was found in queue for masumin
2025-08-04 12:03:06,110 ERROR scheduler Skipped queueing frappe.core.doctype.prepared_report.prepared_report.expire_stalled_report because it was found in queue for masumin
2025-08-04 12:03:06,120 ERROR scheduler Skipped queueing frappe.desk.page.backups.backups.delete_downloadable_backups because it was found in queue for masumin
2025-08-04 12:03:06,134 ERROR scheduler Skipped queueing frappe.desk.form.document_follow.send_hourly_updates because it was found in queue for masumin
2025-08-04 12:03:06,139 ERROR scheduler Skipped queueing frappe.website.doctype.web_page.web_page.check_publish_status because it was found in queue for masumin
2025-08-04 12:03:06,151 ERROR scheduler Skipped queueing frappe.twofactor.delete_all_barcodes_for_users because it was found in queue for masumin
2025-08-04 12:03:06,168 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_type.shift_type.update_last_sync_of_checkin because it was found in queue for masumin
2025-08-04 12:03:06,176 ERROR scheduler Skipped queueing hrms.hr.doctype.shift_schedule_assignment.shift_schedule_assignment.process_auto_shift_creation because it was found in queue for masumin
2025-08-04 12:03:06,178 ERROR scheduler Skipped queueing frappe.model.utils.user_settings.sync_user_settings because it was found in queue for masumin
2025-08-04 12:03:06,188 ERROR scheduler Skipped queueing frappe.website.doctype.personal_data_deletion_request.personal_data_deletion_request.process_data_deletion_request because it was found in queue for masumin
2025-08-04 12:03:06,191 ERROR scheduler Skipped queueing frappe.oauth.delete_oauth2_data because it was found in queue for masumin
2025-08-04 12:57:46,809 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for masumin
2025-08-04 12:57:46,869 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for masumin
2025-08-04 12:57:46,906 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for masumin
2025-08-04 12:58:55,267 ERROR scheduler Skipped queueing hrms.hr.doctype.interview.interview.send_interview_reminder because it was found in queue for masumin
2025-08-04 12:58:55,920 ERROR scheduler Skipped queueing frappe.email.queue.flush because it was found in queue for masumin
2025-08-04 12:58:55,949 ERROR scheduler Skipped queueing frappe.integrations.doctype.google_calendar.google_calendar.sync because it was found in queue for masumin
